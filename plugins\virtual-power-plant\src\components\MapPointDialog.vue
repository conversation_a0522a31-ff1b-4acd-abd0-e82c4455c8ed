<template>
  <el-dialog
    :title="$T('选取地址')"
    width="50%"
    :visible.sync="visible"
    :before-close="handleClose"
    class="map-point-dialog"
  >
    <div class="map-container">
      <div class="coordinates-display">
        {{ $T("经度") }}：{{ currentLng }} {{ $T("纬度") }}：{{ currentLat }}
      </div>
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          ref="searchInput"
          v-model="searchKeyword"
          type="text"
          :placeholder="$T('请输入地址或地点名称')"
          class="search-input"
        />
      </div>
      <div id="baiduMap" class="baidu-map" ref="mapContainer"></div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button
        type="primary"
        @click="confirmSelection"
        :disabled="!selectedPoint"
      >
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { api } from "@altair/knight";

export default {
  name: "MapPointDialog",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    defaultPoint: {
      type: Object,
      default: () => ({
        lng: 116.404,
        lat: 39.915
      })
    }
  },
  data() {
    return {
      visible: this.value,
      map: null,
      marker: null,
      selectedPoint: null,
      geolocation: null,
      searchKeyword: "",
      currentLng: "26.598194",
      currentLat: "106.70741",
      localSearch: null,
      autocomplete: null,
      mapContainer: null
    };
  },
  watch: {
    value(newVal) {
      this.visible = newVal;
      if (newVal) {
        this.$nextTick(() => {
          this.initBaiduMap();
        });
      }
    },
    visible(newVal) {
      this.$emit("input", newVal);
    }
  },
  methods: {
    /**
     * 初始化百度地图资源
     */
    async loadBaiduMapResources() {
      try {
        await api.loadBaiduMapResource({
          jsUrls: [
            "/static/baiduMap/TextIconOverlay.js",
            "/static/baiduMap/NewRichMarker.min.js",
            "/static/baiduMap/CurveLine.js"
          ],
          cssUrls: ["http://api.map.baidu.com/res/webgl/10/bmap.css"],
          onlineJsUrls: [
            `http://api.map.baidu.com/getscript?v=3.0&ak=ulIkqnv3mXwT02LuAl7qdzI2QDTFQCVJ&services=&t=20240515114120`
          ]
        });
        console.log("百度地图资源加载完成");
      } catch (error) {
        console.error("百度地图资源加载失败:", error);
        this.$message.error(this.$T("地图资源加载失败"));
      }
    },

    /**
     * 初始化百度地图
     */
    async initBaiduMap() {
      if (!window.BMap) {
        await this.loadBaiduMapResources();
      }

      // 等待DOM渲染完成
      await this.$nextTick();

      //   const mapContainer = document.getElementById("baiduMap");
      //   if (!mapContainer) {
      //     console.error("地图容器未找到");
      //     return;
      //   }

      // 创建地图实例
      this.map = new window.BMap.Map(this.$refs.mapContainer);

      // 设置中心点和缩放级别
      const point = new window.BMap.Point(
        this.defaultPoint.lng,
        this.defaultPoint.lat
      );
      this.map.centerAndZoom(point, 15);

      // 启用鼠标滚轮缩放
      this.map.enableScrollWheelZoom();

      // 添加点击事件
      this.map.addEventListener("click", this.onMapClick);

      // 如果有默认点，添加标记
      if (this.defaultPoint.lng && this.defaultPoint.lat) {
        this.addMarker(this.defaultPoint);
      } else {
        // 更新经纬度显示为地图中心点
        this.updateCoordinatesDisplay({
          lng: this.defaultPoint.lng,
          lat: this.defaultPoint.lat
        });
      }

      // 初始化自动完成功能
      this.initAutoComplete();
    },

    /**
     * 地图点击事件处理
     */
    onMapClick(e) {
      const point = {
        lng: e.point.lng,
        lat: e.point.lat
      };

      this.addMarker(point);
      this.getAddressByPoint(point);
      this.updateCoordinatesDisplay(point);
    },

    /**
     * 添加标记点
     */
    addMarker(point) {
      // 清除之前的标记
      if (this.marker) {
        this.map.removeOverlay(this.marker);
      }

      // 创建新标记
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);
      this.marker = new window.BMap.Marker(baiduPoint);
      this.map.addOverlay(this.marker);

      // 设置选中点
      this.selectedPoint = { ...point };

      // 更新经纬度显示
      this.updateCoordinatesDisplay(point);

      // 居中显示
      this.map.panTo(baiduPoint);
    },

    /**
     * 根据坐标获取地址
     */
    getAddressByPoint(point) {
      if (!window.BMap) return;

      const geoc = new window.BMap.Geocoder();
      const baiduPoint = new window.BMap.Point(point.lng, point.lat);

      geoc.getLocation(baiduPoint, result => {
        if (result && this.selectedPoint) {
          this.selectedPoint.address = result.address;
        }
      });
    },

    /**
     * 更新经纬度显示
     */
    updateCoordinatesDisplay(point) {
      this.currentLng = point.lng;
      this.currentLat = point.lat;
    },

    /**
     * 获取当前位置
     */
    getCurrentLocation() {
      if (!window.BMap) return;

      if (!this.geolocation) {
        this.geolocation = new window.BMap.Geolocation();
      }

      this.geolocation.getCurrentPosition(result => {
        if (this.geolocation.getStatus() === window.BMAP_STATUS_SUCCESS) {
          const point = {
            lng: result.point.lng,
            lat: result.point.lat
          };

          this.addMarker(point);
          this.getAddressByPoint(point);
          this.updateCoordinatesDisplay(point);
          this.$message.success(this.$T("定位成功"));
        } else {
          this.$message.error(this.$T("定位失败，请检查浏览器定位权限"));
        }
      });
    },

    /**
     * 初始化自动完成功能
     */
    initAutoComplete() {
      if (!window.BMap || !this.map || !this.$refs.searchInput) {
        return;
      }

      // 创建自动完成实例
      this.autocomplete = new window.BMap.Autocomplete({
        input: this.$refs.searchInput,
        location: this.map
      });

      // 监听选择事件
      this.autocomplete.addEventListener("onconfirm", e => {
        const _value = e.item.value;
        const searchValue =
          _value.province +
          _value.city +
          _value.district +
          _value.street +
          _value.business;

        // 执行搜索并定位
        this.setPlace(searchValue);
      });
    },

    /**
     * 设置地点并标记
     */
    setPlace(searchValue) {
      if (!window.BMap || !this.map) {
        return;
      }

      // 清除之前的标记
      if (this.marker) {
        this.map.removeOverlay(this.marker);
      }

      // 创建本地搜索实例
      const localSearch = new window.BMap.LocalSearch(this.map, {
        onSearchComplete: () => {
          if (localSearch.getStatus() === window.BMAP_STATUS_SUCCESS) {
            const poi = localSearch.getResults().getPoi(0);
            if (poi && poi.point) {
              const point = {
                lng: poi.point.lng,
                lat: poi.point.lat
              };

              // 添加标记并更新显示
              this.addMarker(point);
              this.getAddressByPoint(point);
              this.$message.success(this.$T("搜索成功"));
            } else {
              this.$message.warning(this.$T("未找到相关地点"));
            }
          } else {
            this.$message.error(this.$T("搜索失败，请重试"));
          }
        }
      });

      // 执行搜索
      localSearch.search(searchValue);
    },

    /**
     * 搜索地点
     */
    searchLocation() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning(this.$T("请输入搜索关键词"));
        return;
      }

      if (!window.BMap || !this.map) {
        this.$message.error(this.$T("地图未初始化"));
        return;
      }

      // 创建搜索实例
      if (!this.localSearch) {
        this.localSearch = new window.BMap.LocalSearch(this.map, {
          onSearchComplete: results => {
            if (this.localSearch.getStatus() === window.BMAP_STATUS_SUCCESS) {
              const poi = results.getPoi(0);
              if (poi && poi.point) {
                const point = {
                  lng: poi.point.lng,
                  lat: poi.point.lat
                };

                this.addMarker(point);
                this.getAddressByPoint(point);
                this.updateCoordinatesDisplay(point);
                this.$message.success(this.$T("搜索成功"));
              } else {
                this.$message.warning(this.$T("未找到相关地点"));
              }
            } else {
              this.$message.error(this.$T("搜索失败，请重试"));
            }
          }
        });
      }

      // 执行搜索
      this.localSearch.search(this.searchKeyword.trim());
    },

    /**
     * 确认选择
     */
    confirmSelection() {
      if (!this.selectedPoint) {
        this.$message.warning(this.$T("请先选择一个位置"));
        return;
      }

      this.$emit("confirm", this.selectedPoint);
      this.handleClose();
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.visible = false;
      this.selectedPoint = null;
      this.searchKeyword = "";

      // 清理地图资源
      if (this.map) {
        this.map.removeEventListener("click", this.onMapClick);
        if (this.marker) {
          this.map.removeOverlay(this.marker);
        }
        this.map = null;
        this.marker = null;
      }

      // 清理搜索实例
      this.localSearch = null;
      this.autocomplete = null;
    }
  },

  mounted() {
    // 预加载百度地图资源
    this.loadBaiduMapResources();
  },

  beforeDestroy() {
    this.handleClose();
  }
};
</script>

<style lang="scss" scoped>
.map-point-dialog {
  .map-container {
    position: relative;
    height: 500px;

    .coordinates-display {
      text-align: center;
      margin-bottom: var(--J2);
    }

    .search-container {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;

      .search-input {
        width: 240px;
      }
    }

    .baidu-map {
      width: 100%;
      //   height: 100%;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>

<style>
/* 百度地图自动完成下拉框样式 */
.tangram-suggestion-main {
  z-index: 30000 !important;
}
</style>
